[{"D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx": "1", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx": "2", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts": "3", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts": "4", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx": "5", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx": "6", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx": "7", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx": "8", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx": "9", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx": "10", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx": "11", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx": "12", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx": "13", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx": "14", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts": "15", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts": "16", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts": "17", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts": "18", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts": "19", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts": "20", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts": "21", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts": "22", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts": "23", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts": "24", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts": "25", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts": "26", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts": "27", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts": "28", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts": "29", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts": "30", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts": "31", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts": "32", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts": "33", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts": "34", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts": "35", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts": "36", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx": "37", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx": "38", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx": "39", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx": "40", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx": "41", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx": "42", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts": "43", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx": "44", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx": "45", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx": "46", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts": "47", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx": "48", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx": "49", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx": "50", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx": "51", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx": "52", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx": "53", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx": "54", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx": "55", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx": "56", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx": "57", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx": "58", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx": "59", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx": "60", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts": "61", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx": "62", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx": "63", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts": "64", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx": "65", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx": "66", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\ContractPayment.ts": "67", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ContractSuccessAlert.tsx": "68", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\MultipleContractPaymentForm.tsx": "69", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentHistoryDialog.tsx": "70"}, {"size": 591, "mtime": 1748515448800, "results": "71", "hashOfConfig": "72"}, {"size": 1793, "mtime": 1748515469191, "results": "73", "hashOfConfig": "72"}, {"size": 468, "mtime": 1747923851728, "results": "74", "hashOfConfig": "72"}, {"size": 138, "mtime": 1747902225279, "results": "75", "hashOfConfig": "72"}, {"size": 2839, "mtime": 1748514804001, "results": "76", "hashOfConfig": "72"}, {"size": 8908, "mtime": 1748533578929, "results": "77", "hashOfConfig": "72"}, {"size": 3735, "mtime": 1748527080783, "results": "78", "hashOfConfig": "72"}, {"size": 15850, "mtime": 1748541705346, "results": "79", "hashOfConfig": "72"}, {"size": 10762, "mtime": 1748529335878, "results": "80", "hashOfConfig": "72"}, {"size": 1078, "mtime": 1747902509716, "results": "81", "hashOfConfig": "72"}, {"size": 28838, "mtime": 1747994625869, "results": "82", "hashOfConfig": "72"}, {"size": 1722, "mtime": 1748514819539, "results": "83", "hashOfConfig": "72"}, {"size": 571, "mtime": 1747904248056, "results": "84", "hashOfConfig": "72"}, {"size": 605, "mtime": 1747902220275, "results": "85", "hashOfConfig": "72"}, {"size": 4372, "mtime": 1747932197380, "results": "86", "hashOfConfig": "72"}, {"size": 5865, "mtime": 1748527152322, "results": "87", "hashOfConfig": "72"}, {"size": 790, "mtime": 1747906005248, "results": "88", "hashOfConfig": "72"}, {"size": 5008, "mtime": 1748529003240, "results": "89", "hashOfConfig": "72"}, {"size": 1363, "mtime": 1747907859946, "results": "90", "hashOfConfig": "72"}, {"size": 11316, "mtime": 1748530836259, "results": "91", "hashOfConfig": "72"}, {"size": 213, "mtime": 1747922856213, "results": "92", "hashOfConfig": "72"}, {"size": 294, "mtime": 1748540672041, "results": "93", "hashOfConfig": "72"}, {"size": 488, "mtime": 1748243825884, "results": "94", "hashOfConfig": "72"}, {"size": 368, "mtime": 1747928054242, "results": "95", "hashOfConfig": "72"}, {"size": 492, "mtime": 1748541640454, "results": "96", "hashOfConfig": "72"}, {"size": 426, "mtime": 1748533519188, "results": "97", "hashOfConfig": "72"}, {"size": 3543, "mtime": 1747998520066, "results": "98", "hashOfConfig": "72"}, {"size": 9179, "mtime": 1748532286205, "results": "99", "hashOfConfig": "72"}, {"size": 252, "mtime": 1747922140447, "results": "100", "hashOfConfig": "72"}, {"size": 292, "mtime": 1747998489731, "results": "101", "hashOfConfig": "72"}, {"size": 155, "mtime": 1747901999771, "results": "102", "hashOfConfig": "72"}, {"size": 625, "mtime": 1748529003179, "results": "103", "hashOfConfig": "72"}, {"size": 346, "mtime": 1747902012555, "results": "104", "hashOfConfig": "72"}, {"size": 619, "mtime": 1748540653105, "results": "105", "hashOfConfig": "72"}, {"size": 1849, "mtime": 1747994411130, "results": "106", "hashOfConfig": "72"}, {"size": 4238, "mtime": 1747931208285, "results": "107", "hashOfConfig": "72"}, {"size": 10010, "mtime": 1748524578686, "results": "108", "hashOfConfig": "72"}, {"size": 13075, "mtime": 1748529003093, "results": "109", "hashOfConfig": "72"}, {"size": 2838, "mtime": 1747921471664, "results": "110", "hashOfConfig": "72"}, {"size": 21308, "mtime": 1748530332948, "results": "111", "hashOfConfig": "72"}, {"size": 8890, "mtime": 1748524634047, "results": "112", "hashOfConfig": "72"}, {"size": 6105, "mtime": 1748529499221, "results": "113", "hashOfConfig": "72"}, {"size": 4375, "mtime": 1748540712615, "results": "114", "hashOfConfig": "72"}, {"size": 7009, "mtime": 1748449027232, "results": "115", "hashOfConfig": "72"}, {"size": 3188, "mtime": 1747932337817, "results": "116", "hashOfConfig": "72"}, {"size": 7570, "mtime": 1748526924176, "results": "117", "hashOfConfig": "72"}, {"size": 948, "mtime": 1747902060729, "results": "118", "hashOfConfig": "72"}, {"size": 3892, "mtime": 1747910324864, "results": "119", "hashOfConfig": "72"}, {"size": 7270, "mtime": 1748529480804, "results": "120", "hashOfConfig": "72"}, {"size": 976, "mtime": 1748524477225, "results": "121", "hashOfConfig": "72"}, {"size": 6278, "mtime": 1748524679782, "results": "122", "hashOfConfig": "72"}, {"size": 2227, "mtime": 1748524457724, "results": "123", "hashOfConfig": "72"}, {"size": 8727, "mtime": 1748541625189, "results": "124", "hashOfConfig": "72"}, {"size": 1678, "mtime": 1748524469924, "results": "125", "hashOfConfig": "72"}, {"size": 564, "mtime": 1748524510466, "results": "126", "hashOfConfig": "72"}, {"size": 2042, "mtime": 1748526905685, "results": "127", "hashOfConfig": "72"}, {"size": 6200, "mtime": 1748529458458, "results": "128", "hashOfConfig": "72"}, {"size": 1303, "mtime": 1748524489455, "results": "129", "hashOfConfig": "72"}, {"size": 10874, "mtime": 1748423770679, "results": "130", "hashOfConfig": "72"}, {"size": 2257, "mtime": 1747912064565, "results": "131", "hashOfConfig": "72"}, {"size": 1351, "mtime": 1747932215756, "results": "132", "hashOfConfig": "72"}, {"size": 3267, "mtime": 1747978131060, "results": "133", "hashOfConfig": "72"}, {"size": 2361, "mtime": 1748527287211, "results": "134", "hashOfConfig": "72"}, {"size": 120, "mtime": 1747907779789, "results": "135", "hashOfConfig": "72"}, {"size": 5715, "mtime": 1748524444570, "results": "136", "hashOfConfig": "72"}, {"size": 12103, "mtime": 1748524827513, "results": "137", "hashOfConfig": "72"}, {"size": 736, "mtime": 1748540641124, "results": "138", "hashOfConfig": "72"}, {"size": 2388, "mtime": 1748533587994, "results": "139", "hashOfConfig": "72"}, {"size": 12527, "mtime": 1748540769490, "results": "140", "hashOfConfig": "72"}, {"size": 8443, "mtime": 1748541554974, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10nwx99", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx", [], ["352"], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\ContractPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ContractSuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\MultipleContractPaymentForm.tsx", ["353", "354"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentHistoryDialog.tsx", ["355"], [], {"ruleId": "356", "severity": 1, "message": "357", "line": 592, "column": 6, "nodeType": "358", "endLine": 592, "endColumn": 8, "suggestions": "359", "suppressions": "360"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 34, "column": 8, "nodeType": "363", "messageId": "364", "endLine": 34, "endColumn": 24}, {"ruleId": "361", "severity": 1, "message": "365", "line": 43, "column": 10, "nodeType": "363", "messageId": "364", "endLine": 43, "endColumn": 29}, {"ruleId": "356", "severity": 1, "message": "366", "line": 54, "column": 6, "nodeType": "358", "endLine": 54, "endColumn": 24, "suggestions": "367"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCustomerStatistics'. Either include it or remove the dependency array.", "ArrayExpression", ["368"], ["369"], "@typescript-eslint/no-unused-vars", "'InfoOutlinedIcon' is defined but never used.", "Identifier", "unusedVar", "'formatDateLocalized' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPaymentHistory'. Either include it or remove the dependency array.", ["370"], {"desc": "371", "fix": "372"}, {"kind": "373", "justification": "374"}, {"desc": "375", "fix": "376"}, "Update the dependencies array to be: [loadCustomerStatistics]", {"range": "377", "text": "378"}, "directive", "", "Update the dependencies array to be: [open, contractId, fetchPaymentHistory]", {"range": "379", "text": "380"}, [22117, 22119], "[loadCustomerStatistics]", [1358, 1376], "[open, contractId, fetchPaymentHistory]"]