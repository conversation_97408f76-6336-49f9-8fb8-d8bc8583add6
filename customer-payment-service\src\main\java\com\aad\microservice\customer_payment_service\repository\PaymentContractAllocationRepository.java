package com.aad.microservice.customer_payment_service.repository;

import com.aad.microservice.customer_payment_service.model.PaymentContractAllocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaymentContractAllocationRepository extends JpaRepository<PaymentContractAllocation, Long> {
    
    // Tìm tất cả phân bổ thanh toán theo contract ID
    List<PaymentContractAllocation> findByContractId(Long contractId);
    
    // Tìm tất cả phân bổ thanh toán theo payment ID
    List<PaymentContractAllocation> findByPaymentId(Long paymentId);
    
    // Tính tổng số tiền đã thanh toán cho một hợp đồng
    @Query("SELECT SUM(pca.allocatedAmount) FROM PaymentContractAllocation pca WHERE pca.contractId = :contractId")
    Double getTotalPaidAmountByContractId(@Param("contractId") Long contractId);
    
    // Tìm tất cả phân bổ thanh toán theo customer ID (thông qua payment)
    @Query("SELECT pca FROM PaymentContractAllocation pca JOIN pca.payment p WHERE p.customerId = :customerId")
    List<PaymentContractAllocation> findByCustomerId(@Param("customerId") Long customerId);
    
    // Kiểm tra xem một hợp đồng đã có thanh toán nào chưa
    boolean existsByContractId(Long contractId);
    
    // Xóa tất cả phân bổ thanh toán của một payment
    void deleteByPaymentId(Long paymentId);
}
